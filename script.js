/**
 * <PERSON>'s timer
 * A modern timer for speedcubing with statistics, inspection mode, and Bluetooth support
 */

// DOM Element References
const clearSessionBtn = document.getElementById("clearSessionBtn");
const connectBtn = document.getElementById("connectBtn");
const exportBtn = document.getElementById("exportBtn");
const refreshBtn = document.getElementById("refreshBtn");
const prevBtn = document.getElementById("prevBtn");
const latestTime = document.getElementById("latestTime");
const timesList = document.getElementById("timesList");
const scrambleEl = document.getElementById("scramble");
const scrambleImg = document.getElementById("scrambleImg");
const scrambleSpinner = document.getElementById("scrambleSpinner");
const timerArea = document.getElementById("timerArea");
const tabCurrent = document.getElementById("tab-current");
const tabBest = document.getElementById("tab-best");
const tabTrend = document.getElementById("tab-trend");
const distributionModal = document.getElementById("distributionModal");
const distributionModalClose = document.getElementById("distributionModalClose");
const distributionChart = document.getElementById("distributionChart");
const inspectionBtn = document.getElementById("inspectionBtn");
const shortcutsBtn = document.getElementById("shortcutsBtn");
const settingsMenu = document.getElementById("settingsMenu");
const settingsBtn = document.getElementById("settingsBtn");
const settingsPanel = document.getElementById("settingsPanel");
const settingsCloseBtn = document.getElementById("settingsCloseBtn");
const scrambleLengthBtn = document.getElementById("scrambleLengthBtn");
const diagramBtn = document.getElementById("diagramBtn");
const darkModeBtn = document.getElementById("darkModeBtn");
const darkModeIcon = document.getElementById("darkModeIcon");
const body = document.getElementById("body");
const header = document.querySelector(".header");

let settingsPanelCloseTimer = null;

// Dark Mode Management
let darkMode = "system"; // Current mode: "system", "light", "dark"

// Update dark mode button UI to reflect current state
function setDarkModeUI(mode) {
  const configs = {
    dark: { icon: "fa-solid fa-moon", label: "Dark", active: true },
    light: { icon: "fa-solid fa-sun", label: "Light", active: true },
    system: { icon: "fa-solid fa-circle-half-stroke", label: "System", active: false }
  };

  const config = configs[mode];
  darkModeIcon.className = config.icon;
  darkModeBtn.querySelector(".label").textContent = config.label;

  if (config.active) {
    darkModeBtn.classList.add("bg-blue-500", "text-white");
    darkModeBtn.classList.remove("hover:bg-gray-200", "dark:hover:bg-gray-700");
  } else {
    darkModeBtn.classList.remove("bg-blue-500", "text-white");
    darkModeBtn.classList.add("hover:bg-gray-200", "dark:hover:bg-gray-700");
  }
}

function applyDarkMode(mode) {
  body.classList.remove("dark-mode", "light-mode");

  if (mode === "dark") {
    body.classList.add("dark-mode");
  } else if (mode === "light") {
    body.classList.add("light-mode");
  }
  // For "system", we don't add any class, letting CSS media queries handle it
}

function loadDarkModeState() {
  const saved = localStorage.getItem("darkMode");
  darkMode = saved || "system";
  setDarkModeUI(darkMode);
  applyDarkMode(darkMode);
}

function saveDarkModeState(mode) {
  localStorage.setItem("darkMode", mode);
}

darkModeBtn.addEventListener("click", () => {
  toggleDarkMode(false);
});

function toggleDarkMode(notify) {
  if (darkMode === "system") {
    darkMode = "dark";
  } else if (darkMode === "dark") {
    darkMode = "light";
  } else {
    darkMode = "system";
  }
  setDarkModeUI(darkMode);
  applyDarkMode(darkMode);
  saveDarkModeState(darkMode);

  if (notify) {
    showSwal({
      text: "Dark mode toggled to " + darkMode + ".",
      icon: "success"
    });
  }
}

function showSettingsPanel() {
  settingsMenu.setAttribute("data-open", "true");
  header.classList.add("settings-open");
  settingsBtn.classList.add("opacity-0", "pointer-events-none");
  settingsBtn.classList.remove("opacity-100");
  settingsPanel.classList.remove("opacity-0", "pointer-events-none");
  settingsPanel.classList.add("opacity-100", "pointer-events-auto");
}
function hideSettingsPanel() {
  settingsMenu.setAttribute("data-open", "false");
  header.classList.remove("settings-open");
  settingsBtn.classList.remove("opacity-0", "pointer-events-none");
  settingsBtn.classList.add("opacity-100");
  settingsPanel.classList.add("opacity-0", "pointer-events-none");
  settingsPanel.classList.remove("opacity-100", "pointer-events-auto");
}

settingsBtn.classList.add("opacity-100");
settingsPanel.classList.add("opacity-0", "pointer-events-none");

settingsBtn.addEventListener("click", (e) => {
  e.stopPropagation();
  if (settingsMenu.getAttribute("data-open") === "true") {
    hideSettingsPanel();
  } else {
    showSettingsPanel();
  }
});
settingsCloseBtn.addEventListener("click", (e) => {
  e.stopPropagation();
  hideSettingsPanel();
});
document.addEventListener("click", (e) => {
  if (!settingsMenu.contains(e.target)) {
    hideSettingsPanel();
  }
});

// Application state
let currentScramble = "";
let scrambleHistory = [];
let currentScrambleIndex = -1;
let solveTimes = JSON.parse(localStorage.getItem("solveTimes")) || [];
let scrambleLength = parseInt(localStorage.getItem("scrambleLength")) || 15;

// Timer state
let manualRunning = false;
let manualStartTime = 0;
let readyToStart = false;
let running = false;
let runningStart = 0;
let runningInterval = null;
let touchReady = false;
let keyReady = false;
let readinessTimer = null;

// Inspection state
let inspectionMode = false;
let inspecting = false;
let inspectionStartTime = 0;
let inspectionInterval = null;
let inspectionAnnounced8 = false;
let inspectionAnnounced12 = false;
let inspectionStartup = false;
let inspectionStartupTimer = null;

// UI state
let showingStatMode = "current";
let previousStatMode = "current";
let selectedSolveIndex = null;
let chartInstance = null;
let ganConnected = false;

const GanTimerState = {
  DISCONNECT: 0,
  GET_SET: 1,
  HANDS_OFF: 2,
  RUNNING: 3,
  STOPPED: 4,
  IDLE: 5,
  HANDS_ON: 6,
  FINISHED: 7
};

// Feature state
let diagramOn = true;
// Update button UI state (active/inactive)
function setButtonState(button, isActive) {
  if (isActive) {
    button.classList.add("bg-blue-500", "text-white");
    button.classList.remove("hover:bg-gray-200", "dark:hover:bg-gray-700");
  } else {
    button.classList.remove("bg-blue-500", "text-white");
    button.classList.add("hover:bg-gray-200", "dark:hover:bg-gray-700");
  }
}

function setDiagramBtnUI(on) {
  setButtonState(diagramBtn, on);
}
function loadDiagramState() {
  const saved = localStorage.getItem("diagramOn");
  diagramOn = saved === null ? true : saved === "true";
  setDiagramBtnUI(diagramOn);
  updateDiagramVisibility();
}
function saveDiagramState(on) {
  localStorage.setItem("diagramOn", on ? "true" : "false");
}
diagramBtn.addEventListener("click", () => {
  diagramOn = !diagramOn;
  setDiagramBtnUI(diagramOn);
  saveDiagramState(diagramOn);
  updateDiagramVisibility();
});
function updateDiagramVisibility() {
  const img = document.getElementById("scrambleImg");
  const spinner = document.getElementById("scrambleSpinner");
  if (diagramOn) {
    img.style.display = "";
    spinner.style.display = "";
  } else {
    img.style.display = "none";
    spinner.style.display = "none";
  }
}

// Application initialization
loadDarkModeState(); // Load dark mode first
loadDiagramState();
displayScramble();
renderTimes();
showStatGroup("current");

function setInspectionBtnUI(on) {
  setButtonState(inspectionBtn, on);
}

function loadInspectionState() {
  const saved = localStorage.getItem("inspectionMode");
  inspectionMode = saved === "true";
  setInspectionBtnUI(inspectionMode);
}

function saveInspectionState(on) {
  localStorage.setItem("inspectionMode", on ? "true" : "false");
}

inspectionBtn.addEventListener("click", () => {
  inspectionMode = !inspectionMode;
  setInspectionBtnUI(inspectionMode);
  saveInspectionState(inspectionMode);
});

loadInspectionState();

// Time distribution chart functions

// Calculate histogram data for time distribution (single solves only)
function calculateTimeDistribution() {
  if (solveTimes.length === 0) return { labels: [], data: [] };

  // Collect all single solve times
  const times = [];
  solveTimes.forEach(solve => {
    if (!solve.dnf) {
      const adjustedTime = solve.ms + (solve.plus2 ? 2000 : 0);
      times.push(adjustedTime / 1000); // Convert to seconds
    }
  });

  if (times.length === 0) return { labels: [], data: [] };

  // Create histogram buckets (1-second intervals)
  const minTime = Math.floor(Math.min(...times));
  const maxTime = Math.ceil(Math.max(...times));
  const buckets = {};

  // Initialize buckets
  for (let i = minTime; i <= maxTime; i++) {
    buckets[i] = 0;
  }

  // Count times in each bucket
  times.forEach(time => {
    const bucket = Math.floor(time);
    if (buckets.hasOwnProperty(bucket)) {
      buckets[bucket]++;
    }
  });

  // Convert to chart format - only include non-zero buckets
  const labels = [];
  const data = [];

  for (let i = minTime; i <= maxTime; i++) {
    if (buckets[i] > 0) {
      labels.push(`${i}.x`);
      data.push(buckets[i]);
    }
  }

  return { labels, data };
}

// Create histogram chart showing time distribution
function createDistributionChart() {
  const ctx = distributionChart.getContext('2d');

  // Destroy existing chart if it exists
  if (chartInstance) {
    chartInstance.destroy();
  }

  const { labels, data } = calculateTimeDistribution();
  const isDarkMode = body.classList.contains('dark-mode') ||
    (!body.classList.contains('light-mode') && window.matchMedia('(prefers-color-scheme: dark)').matches);

  const textColor = isDarkMode ? '#f9fafb' : '#111827';
  const gridColor = isDarkMode ? '#374151' : '#e5e7eb';

  chartInstance = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: labels,
      datasets: [{
        label: 'Count',
        data: data,
        backgroundColor: 'rgba(59, 130, 246, 0.6)',
        borderColor: '#3b82f6',
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        x: {
          title: {
            display: true,
            text: 'Time',
            color: textColor
          },
          ticks: {
            color: textColor
          },
          grid: {
            color: gridColor
          }
        },
        y: {
          title: {
            display: true,
            text: 'Number of solves',
            color: textColor
          },
          ticks: {
            color: textColor,
            stepSize: 1,
            callback: function(value) {
              return Math.floor(value) === value ? value : '';
            }
          },
          grid: {
            color: gridColor
          }
        }
      },
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              const count = context.parsed.y;
              return `${count} solve${count !== 1 ? 's' : ''}`;
            }
          }
        }
      }
    }
  });
}

function showDistributionModal() {
  previousStatMode = showingStatMode;
  showingStatMode = "trend";
  updateTabStyles();

  // Hide main page content
  const mainCard = document.querySelector('.main-card');
  if (mainCard) {
    mainCard.style.display = 'none';
  }

  distributionModal.classList.remove("hidden");

  // Create chart
  setTimeout(() => {
    createDistributionChart();
  }, 100);
}

function hideDistributionModal() {
  distributionModal.classList.add("hidden");
  showingStatMode = previousStatMode;
  updateTabStyles();

  // Show main page content again
  const mainCard = document.querySelector('.main-card');
  if (mainCard) {
    mainCard.style.display = '';
  }

  if (chartInstance) {
    chartInstance.destroy();
    chartInstance = null;
  }
}

function updateTabStyles() {
  const activeClass = "py-1 px-3 border-b-2 border-blue-500 text-black dark:text-white";
  const inactiveClass = "py-1 px-3 border-b-2 border-transparent text-gray-600 dark:text-gray-300 hover:text-black dark:hover:text-white";

  tabCurrent.className = showingStatMode === "current" ? activeClass : inactiveClass;
  tabBest.className = showingStatMode === "best" ? activeClass : inactiveClass;
  tabTrend.className = showingStatMode === "trend" ? activeClass : inactiveClass;
}

function setConnectBtnUI(connected) {
  setButtonState(connectBtn, connected);
}

// Event Listeners Setup

document.addEventListener("keydown", handleKeyDown);
document.addEventListener("keyup", handleKeyUp);
timerArea.addEventListener("touchstart", handleTouchStart, { passive: false });
document.addEventListener("touchend", handleTouchEnd);
exportBtn.addEventListener("click", exportSessionToCSV);
connectBtn.addEventListener("click", connectGanTimer);
refreshBtn.addEventListener("click", goToNextScramble);
prevBtn.addEventListener("click", goToPreviousScramble);
clearSessionBtn.addEventListener("click", clearSession);
tabCurrent.addEventListener("click", () => {
  showStatGroup("current");
  showingStatMode = "current";
  updateTabStyles();
});
tabBest.addEventListener("click", () => {
  showStatGroup("best");
  showingStatMode = "best";
  updateTabStyles();
});
tabTrend.addEventListener("click", () => {
  showDistributionModal();
});

// Distribution modal event listeners
distributionModalClose.addEventListener("click", hideDistributionModal);

// Close modal when clicking outside
distributionModal.addEventListener("click", (e) => {
  if (e.target === distributionModal) {
    hideDistributionModal();
  }
});

scrambleLengthBtn.addEventListener("click", () => {
  Swal.fire({
    input: "range",
    inputAttributes: {
      min: "5",
      max: "50",
      step: "1"
    },
    inputValue: scrambleLength,
    text: "Scramble length?",
    icon: "question",
    showCancelButton: true,
    confirmButtonText: "Save",
    cancelButtonText: "Cancel",
    customClass: {
      popup: 'swal-default-colors'
    },
    didOpen: () => {
      const slider = document.getElementById("scrambleLengthSlider");
      const valueDisplay = document.getElementById("scrambleLengthValue");

      slider.addEventListener("input", (e) => {
        valueDisplay.textContent = e.target.value;
      });
    }
  }).then((result) => {
    if (result.isConfirmed) {
      const newLength = parseInt(document.getElementById("scrambleLengthSlider").value);
      scrambleLength = newLength;
      localStorage.setItem("scrambleLength", scrambleLength.toString());

      // Reset scramble history and generate new scramble at the new length
      scrambleHistory = [];
      currentScrambleIndex = -1;
      displayScramble();

      showSwal({
        text: `Scramble length set to ${scrambleLength} moves.`,
        icon: "success"
      });
    }
  });
});

shortcutsBtn.addEventListener("click", () => {
  Swal.fire({
    html: `
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
        <div>
          <h3 class='text-base font-semibold mb-2'>Timer operation</h3>
          <ul class='text-sm space-y-1 mb-4'>
            <li><b>Space</b>: Start timer or inspection</li>
            <li><b>Any key</b>: Stop timer</li>
            <li><b>Esc</b>: Cancel inspection</li>
          </ul>
          <h3 class='text-base font-semibold mb-2'>Previous solves</h3>
          <ul class='text-sm space-y-1 mb-4'>
            <li><b>&#8984;+C</b>: Copy most recent solve</li>
            <li><b>&#8984;+1</b>: Remove +2 and DNF</li>
            <li><b>&#8984;+2</b>: Toggle +2</li>
            <li><b>&#8984;+3</b>: Toggle DNF</li>
            <li><b>&#8984;+4</b>: Delete most recent solve</li>
            <li><b>&#8984;+5</b>: Retry scramble</li>
            <li><b>&#8984;+E</b>: Export session data</li>
          </ul>
        </div>
        <div>
          <h3 class='text-base font-semibold mb-2'>Scramble navigation</h3>
          <ul class='text-sm space-y-1 mb-4'>
            <li><b>&#8984;+]</b>: Previous scramble</li>
            <li><b>&#8984;+[</b>: Next scramble</li>
          </ul>
          <h3 class='text-base font-semibold mb-2'>Global settings</h3>
          <ul class='text-sm space-y-1'>
            <li><b>&#8984;+B</b>: Connect Bluetooth timer</li>
            <li><b>&#8984;+I</b>: Toggle inspection mode</li>
            <li><b>&#8984;+D</b>: Toggle diagram</li>
            <li><b>&#8984;+K</b>: Toggle dark mode</li>
          </ul>
        </div>
      </div>
    `,
    icon: "info",
    customClass: {
      popup: 'swal-default-colors'
    }
  });
});

// Event handlers

// Handle keyboard shortcuts and timer control
function handleKeyDown(e) {
  const isCmd = e.metaKey || e.ctrlKey;
  if (isCmd && e.code === "KeyI") {
    e.preventDefault();
    inspectionMode = !inspectionMode;
    setInspectionBtnUI(inspectionMode);
    saveInspectionState(inspectionMode);
    showSwal({
      text: `Inspection mode ${inspectionMode ? "enabled" : "disabled"}.`,
      icon: "info"
    });
    return;
  }
  if (isCmd && e.code === "KeyE") {
    e.preventDefault();
    exportSessionToCSV();
    return;
  }
  if (isCmd && e.code === "KeyB") {
    e.preventDefault();
    connectGanTimer();
    return;
  }
  if (isCmd && e.code === "KeyD") {
    e.preventDefault();
    diagramOn = !diagramOn;
    setDiagramBtnUI(diagramOn);
    saveDiagramState(diagramOn);
    updateDiagramVisibility();
    return;
  }
  if (isCmd && e.code === "KeyK") {
    e.preventDefault();
    toggleDarkMode(true);
    return;
  }
  if (isCmd && e.code === "KeyC") {
    e.preventDefault();
    copyLastSolveToClipboard();
    return;
  }
  if (isCmd && e.code === "Digit1") {
    e.preventDefault();
    // Remove +2 and DNF penalties from the most recent solve only
    if (solveTimes.length > 0) {
      solveTimes[0].plus2 = false;
      solveTimes[0].dnf = false;
      localStorage.setItem("solveTimes", JSON.stringify(solveTimes));
      renderTimes();
    }
    return;
  }
  if (isCmd && e.code === "Digit2") {
    e.preventDefault();
    toggleLastSolveFlag("plus2");
    return;
  }
  if (isCmd && e.code === "Digit3") {
    e.preventDefault();
    toggleLastSolveFlag("dnf");
    return;
  }
  if (isCmd && e.code === "Digit4") {
    e.preventDefault();
    removeLastSolve();
    return;
  }
  if (isCmd && e.code === "Digit5") {
    e.preventDefault();
    retrySolve();
    return;
  }
  if (isCmd && e.code === "BracketLeft") {
    e.preventDefault();
    goToPreviousScramble();
    return;
  }
  if (isCmd && e.code === "BracketRight") {
    e.preventDefault();
    goToNextScramble();
    return;
  }

  // INSPECTION MODE: Start inspection on spacebar (press/release once)
  if (inspectionMode && !inspecting && e.code === "Space" && !e.repeat && !manualRunning && !readyToStart) {
    e.preventDefault();
    return;
  }

  // INSPECTION MODE: After inspection, pressing spacebar starts 0.5s startup
  if (inspectionMode && inspecting && e.code === "Space" && !e.repeat && !manualRunning && !inspectionStartup) {
    e.preventDefault();
    latestTime.className = "text-red-400";
    inspectionStartup = true;
    inspectionStartupTimer = setTimeout(() => {
      latestTime.className = "text-green-400";
      readyToStart = true;
    }, 500);
    return;
  }

  if (e.code === "Space" && !e.repeat && !manualRunning && !readyToStart) {
    e.preventDefault();
    latestTime.textContent = "0.000";
    latestTime.className = "text-red-400";
    readinessTimer = setTimeout(() => {
      readyToStart = true;
      latestTime.className = "text-green-400";
    }, 500);
  }
}

function handleKeyUp(e) {
  // INSPECTION MODE: Start inspection on spacebar (press/release once)
  if (inspectionMode && !inspecting && e.code === "Space" && !e.repeat && !manualRunning && !readyToStart) {
    e.preventDefault();
    startInspection();
    return;
  }
  // INSPECTION MODE: After inspection, releasing spacebar before 0.5s cancels startup and resumes inspection clock
  if (inspectionMode && inspecting && e.code === "Space" && !e.repeat && !manualRunning && inspectionStartup && !readyToStart) {
    e.preventDefault();
    clearTimeout(inspectionStartupTimer);
    inspectionStartup = false;
    latestTime.className = "text-yellow-500";
    // Do not start solve, just keep inspection running
    return;
  }
  // INSPECTION MODE: After inspection, releasing spacebar after 0.5s starts the solve
  if (inspectionMode && inspecting && e.code === "Space" && !e.repeat && !manualRunning && inspectionStartup && readyToStart) {
    e.preventDefault();
    inspectionStartup = false;
    readyToStart = false;
    endInspectionAndStartSolve();
    return;
  }
  if (e.code === "Space") {
    e.preventDefault();
    clearTimeout(readinessTimer);
    if (readyToStart && !manualRunning) {
      readyToStart = false;
      manualRunning = true;
      latestTime.className = "text-black dark:text-white";
      manualStartTime = Date.now();
      runningInterval = setInterval(() => {
        const elapsed = Date.now() - manualStartTime;
        latestTime.textContent = formatTime(elapsed);
      }, 31);
      // Don't stop the timer on the same spacebar release that started it
      return;
    } else if (manualRunning) {
      stopManualTimer();
    } else {
      // Revert to previous color if releasing before 0.5s
      if (inspectionMode && inspecting) {
        latestTime.className = "text-yellow-500";
      } else {
        latestTime.className = "text-black dark:text-white";
      }
      latestTime.textContent = "0.000";
      readyToStart = false;
    }
  }

  // INSPECTION MODE: Cancel inspection with Esc
  if (inspecting && e.code === "Escape") {
    e.preventDefault();
    cancelInspection();
    return;
  }

  // Allow any key to stop the timer when running (except modifier keys and function keys)
  if (manualRunning && !e.metaKey && !e.ctrlKey && !e.altKey && !e.shiftKey &&
      !e.code.startsWith("F") && e.code !== "CapsLock" && e.code !== "Tab") {
    e.preventDefault();
    stopManualTimer();
    return;
  }
}

function handleTouchStart(e) {
  if (!manualRunning && !touchReady) {
    e.preventDefault();
    latestTime.textContent = "0.000";
    latestTime.className = "text-red-400";
    readinessTimer = setTimeout(() => {
      touchReady = true;
      latestTime.className = "text-green-400";
    }, 500);
  }
}

function handleTouchEnd() {
  clearTimeout(readinessTimer);
  if (touchReady && !manualRunning) {
    touchReady = false;
    manualRunning = true;
    latestTime.className = "text-black dark:text-white";
    manualStartTime = Date.now();
    runningInterval = setInterval(() => {
      const elapsed = Date.now() - manualStartTime;
      latestTime.textContent = formatTime(elapsed);
    }, 31);
  } else if (manualRunning) {
    stopManualTimer();
  } else {
    touchReady = false;
    latestTime.textContent = "0.000";
    // Revert to previous color if releasing before 0.5s
    if (inspectionMode && inspecting) {
      latestTime.className = "text-yellow-500";
    } else {
      latestTime.className = "text-black dark:text-white";
    }
  }
}

function clearSession() {
  Swal.fire({
    text: "Clear all solves from this session?",
    icon: "question",
    showCancelButton: true,
    customClass: {
      popup: 'swal-default-colors'
    }
  }).then((result) => {
    if (result.isConfirmed) {
      solveTimes = [];
      localStorage.removeItem("solveTimes");
      renderTimes();
    }
  });
}

// Timer core functions

// Stop timer and record solve
function stopManualTimer() {
  manualRunning = false;
  clearInterval(runningInterval);
  inspecting = false;
  clearInterval(inspectionInterval);
  inspectionStartup = false;
  clearTimeout(inspectionStartupTimer);
  const totalMs = Date.now() - manualStartTime;
  const timeStr = formatTime(totalMs);
  let plus2 = false;
  let dnf = false;
  
  // Inspection penalty if applicable
  if (window._pendingInspectionPenalty) {
    if (window._pendingInspectionPenalty === "plus2") plus2 = true;
    if (window._pendingInspectionPenalty === "dnf") dnf = true;
  }
  
  const solve = {
    time: timeStr,
    ms: totalMs,
    scramble: currentScramble,
    plus2,
    dnf,
    inspection: window._pendingInspectionTime || null
  };
  solveTimes.unshift(solve);
  localStorage.setItem("solveTimes", JSON.stringify(solveTimes));
  latestTime.textContent = timeStr;
  displayScramble();
  renderTimes();
  
  // Clear inspection penalty state
  window._pendingInspectionPenalty = null;
  window._pendingInspectionTime = null;
}

// Connect to GAN smart timer via Bluetooth
async function connectGanTimer() {
  try {
    const available = await navigator.bluetooth.getAvailability();
    if (!available) throw new Error("Bluetooth is not available. Enable it in your device settings.");
    const device = await navigator.bluetooth.requestDevice({
      filters: [{ namePrefix: "GAN" }, { namePrefix: "gan" }, { namePrefix: "Gan" }],
      optionalServices: ["0000fff0-0000-1000-8000-00805f9b34fb"]
    });
    const server = await device.gatt.connect();
    const service = await server.getPrimaryService("0000fff0-0000-1000-8000-00805f9b34fb");
    const characteristic = await service.getCharacteristic("0000fff5-0000-1000-8000-00805f9b34fb");
    await characteristic.startNotifications();
    characteristic.addEventListener("characteristicvaluechanged", handleGanTimerUpdate);
    showSwal({
      text: "Connected to GAN timer.",
      icon: "success"
    });
  } catch (error) {
    showSwal({
      text: "Connection failed: " + error.message,
      icon: "warning"
    });
  }
}

// Generate new 3x3 scramble sequence
function generateNewScramble() {
  const moves = ["R", "L", "U", "D", "F", "B"];
  const modifiers = ["", "'", "2"];

  // Define opposite face pairs
  const oppositeFaces = {
    "R": "L",
    "L": "R",
    "U": "D",
    "D": "U",
    "F": "B",
    "B": "F"
  };

  let scramble = [];
  let lastMove = "";

  while (scramble.length < scrambleLength) {
    // Filter out the last move and its opposite face
    const availableMoves = moves.filter(move =>
      move !== lastMove && move !== oppositeFaces[lastMove]
    );

    const move = availableMoves[Math.floor(Math.random() * availableMoves.length)];
    lastMove = move;
    scramble.push(move + modifiers[Math.floor(Math.random() * modifiers.length)]);
  }

  return scramble.join(" ");
}

function displayScramble() {
  // Generate new scramble and add to history
  const newScramble = generateNewScramble();

  // Remove any scrambles after current index (if user went back and then generated new)
  scrambleHistory = scrambleHistory.slice(0, currentScrambleIndex + 1);

  // Add new scramble to history
  scrambleHistory.push(newScramble);
  currentScrambleIndex = scrambleHistory.length - 1;

  showCurrentScramble();
}

function showCurrentScramble() {
  if (currentScrambleIndex >= 0 && currentScrambleIndex < scrambleHistory.length) {
    currentScramble = scrambleHistory[currentScrambleIndex];
    scrambleEl.textContent = currentScramble;

    const img = document.getElementById("scrambleImg");
    const spinner = document.getElementById("scrambleSpinner");
    spinner.classList.remove("hidden");
    img.classList.add("hidden");
    const newSrc = `https://algs.cuber.pro/visualcube/visualcube.php?fmt=png&size=250&bg=t&alg=x2${encodeURIComponent(currentScramble)}`;
    img.onload = () => {
      spinner.classList.add("hidden");
      img.classList.remove("hidden");
    };
    img.src = newSrc;
    updateDiagramVisibility();
  }

  updateScrambleButtons();
}

function updateScrambleButtons() {
  // Update previous button state
  prevBtn.disabled = currentScrambleIndex <= 0;

  // Update button styling based on disabled state
  if (prevBtn.disabled) {
    prevBtn.classList.add("opacity-50", "cursor-not-allowed");
  } else {
    prevBtn.classList.remove("opacity-50", "cursor-not-allowed");
  }
}

function goToPreviousScramble() {
  if (currentScrambleIndex > 0) {
    currentScrambleIndex--;
    showCurrentScramble();
  }
}

function goToNextScramble() {
  if (currentScrambleIndex < scrambleHistory.length - 1) {
    currentScrambleIndex++;
    showCurrentScramble();
  } else {
    // Generate new scramble if at the end
    displayScramble();
  }
}

function exportSessionToCSV() {
  if (!solveTimes.length) {
    showSwal({
      text: "No solves to export.",
      icon: "warning"
    });
    return;
  }
  Swal.fire({
    input: "text",
    inputLabel: "Export file name?",
    inputValue: "session.csv",
    icon: "question",
    showCancelButton: true,
    inputValidator: (value) => {
      if (value.trim().length < 5) {
        return "File name must be at least 5 characters long";
      }
      if (!value.endsWith(".csv")) {
        return "File name must end with .csv";
      }
    },
    didOpen: () => {
      const input = Swal.getInput();
      if (input) {
        // Select only the "session" part, leaving ".csv" unselected
        const filename = input.value;
        const dotIndex = filename.lastIndexOf(".csv");
        if (dotIndex > 0) {
          input.setSelectionRange(0, dotIndex);
        }
        input.focus();
      }
    }
  }).then((result) => {
    if (result.isConfirmed) {
      const header = ["#", "Time", "Scramble", "+2?", "DNF?"];
      const rows = solveTimes.map((s, i) => {
        let timeSec = (s.ms + (s.plus2 ? 2000 : 0)) / 1000;
       return [
          solveTimes.length - i,
          timeSec.toFixed(3),
          '"' + s.scramble.replace(/"/g, '""') + '"',
          s.plus2 ? 'Yes' : '',
          s.dnf ? 'Yes' : ''
        ];
      });
      const csvContent = [header, ...rows].map(row => row.join(",")).join("\n");
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute("download", result.value);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  });
}

function handleGanTimerUpdate(event) {
  const dv = event.target.value;
  if (!dv || dv.byteLength === 0 || dv.getUint8(0) !== 254) return;
  try {
    const checksum = dv.getUint16(dv.byteLength - 2, true);
    const dataSlice = dv.buffer.slice(2, dv.byteLength - 2);
    const view = new DataView(dataSlice);
    let crc = 0xffff;
    for (let i = 0; i < view.byteLength; ++i) {
      crc ^= view.getUint8(i) << 8;
      for (let b = 0; b < 8; b++) {
        crc = (crc & 0x8000) ? (crc << 1) ^ 0x1021 : crc << 1;
      }
    }
    crc &= 0xffff;
    if (checksum !== crc) return;
    const state = dv.getUint8(3);
    // Set connect button UI on connect/disconnect
    if (state === GanTimerState.DISCONNECT) {
      ganConnected = false;
      setConnectBtnUI(false);
    } else if (!ganConnected) {
      ganConnected = true;
      setConnectBtnUI(true);
    }
    // If user aborts GAN timer startup (HANDS_OFF or IDLE) during inspection startup, revert to inspection color
    if ((state === GanTimerState.HANDS_OFF || state === GanTimerState.IDLE) && inspectionStartup && inspecting) {
      latestTime.className = "text-yellow-500";
      inspectionStartup = false;
      clearTimeout(inspectionStartupTimer);
    }
    if (state === GanTimerState.RUNNING) {
      latestTime.className = "text-black dark:text-white";
      if (!running) {
        // If inspection is running, stop it as if the solve is starting
        if (inspecting) {
          inspecting = false;
          clearInterval(inspectionInterval);
          inspectionStartup = false;
          clearTimeout(inspectionStartupTimer);
          latestTime.className = "text-black dark:text-white";
        }
        running = true;
        runningStart = Date.now();
        runningInterval = setInterval(() => {
          const elapsed = Date.now() - runningStart;
          latestTime.textContent = formatTime(elapsed);
        }, 31);
      }
    } else if (state === GanTimerState.STOPPED && running) {
      running = false;
      clearInterval(runningInterval);
      const minutes = dv.getUint8(4);
      const seconds = dv.getUint8(5);
      const milliseconds = dv.getUint16(6, true);
      const totalMs = minutes * 60000 + seconds * 1000 + milliseconds;
      const timeStr = formatTime(totalMs);
      const solve = {
        time: timeStr,
        ms: totalMs,
        scramble: currentScramble,
        plus2: false,
        dnf: false
      };
      solveTimes.unshift(solve);
      localStorage.setItem("solveTimes", JSON.stringify(solveTimes));
      latestTime.textContent = timeStr;
      displayScramble();
      renderTimes();
    } else if ((state === GanTimerState.IDLE && !manualRunning) || (state === GanTimerState.DISCONNECT)) {
      latestTime.textContent = "0.000";
      latestTime.className = "text-black dark:text-white";
    } else if (state !== GanTimerState.RUNNING && running) {
      running = false;
      clearInterval(runningInterval);
      latestTime.textContent = "0.000";
      latestTime.className = "text-black dark:text-white";
    } else if (state === GanTimerState.GET_SET) {
      latestTime.className = "text-green-400";
    } else if (state === GanTimerState.HANDS_ON) {
      latestTime.className = "text-red-400";
    } else if (state === GanTimerState.HANDS_OFF && !running && !manualRunning) {
      // Revert color when hands are removed before 0.5s
      if (inspectionMode && inspecting) {
        latestTime.className = "text-yellow-500";
      } else {
        latestTime.className = "text-black dark:text-white";
      }
    }
  } catch (e) {
    console.error("Failed to parse GAN timer data:", e);
  }
}

// UI rendering functions

// Render solve times list and statistics
function renderTimes() {
  timesList.innerHTML = "";
  solveTimes.forEach((solve, i) => {
    const li = document.createElement("li");
    li.className = "flex flex-wrap sm:flex-nowrap items-start gap-2 bg-gray-50 dark:bg-gray-700 p-2 sm:p-3 rounded-md shadow";
    li.onclick = () => showSolveModal(i);
    const info = document.createElement("div");
    info.className = "flex-1 min-w-[10rem]";
    const rawTime = formatTime(solve.ms);
    const adjustedTime = solve.ms + (solve.plus2 ? 2000 : 0);
    const finalTime = formatTime(adjustedTime);
    let displayTime = solve.dnf
      ? "DNF"
      : solve.plus2
        ? `${rawTime} + 2 = ${finalTime}`
        : finalTime;
    info.innerHTML = `<strong>#${solveTimes.length - i}</strong>: ${displayTime}${displayTime !== "DNF" && solve.dnf ? " (DNF)" : ""}<br><span class='text-md font-mono'>${solve.scramble}</span>`;
    li.appendChild(info);
    timesList.appendChild(li);
    timesList.scrollTo({ top: 0, behavior: "smooth" });
  });
  const sessionSec = document.getElementById("session");
  if (solveTimes.length > 0) {
    sessionSec.style.display = "";
    const single = solveTimes[0] ? (solveTimes[0].dnf ? "DNF" : formatTime(solveTimes[0].ms + (solveTimes[0].plus2 ? 2000 : 0))) : "--";
    const ao5 = getAverage(solveTimes, 5);
    const ao12 = getAverage(solveTimes, 12);
    const ao100 = getAverage(solveTimes, 100);
    const ao1000 = getAverage(solveTimes, 1000);
    const bestSingle = getBestSingle(solveTimes);
    const best5 = getBestAverage(solveTimes, 5);
    const best12 = getBestAverage(solveTimes, 12);
    const best100 = getBestAverage(solveTimes, 100);
    const best1000 = getBestAverage(solveTimes, 1000);
    const averagesList = document.getElementById("averagesList");
    averagesList.innerHTML = ``;
    const averageStats = [
      { label: "Single", current: single, best: bestSingle, min: 1 },
      { label: "Ao5", current: ao5, best: best5, format: true, min: 5 },
      { label: "Ao12", current: ao12, best: best12, format: true, min: 12 },
      { label: "Ao100", current: ao100, best: best100, format: true, min: 100 },
      { label: "Ao1000", current: ao1000, best: best1000, format: true, min: 1000 }
    ];
    for (const stat of averageStats) {
      if (stat.current == null && stat.best == null) continue;
      const hasCurrent = stat.current != null && solveTimes.length >= stat.min;
      const hasBest = stat.best != null && solveTimes.length >= stat.min;
      if (!hasCurrent && !hasBest) continue;
      const header = document.createElement("li");
      header.className = "text-center font-semibold";
      header.textContent = stat.label;
      averagesList.appendChild(header);
      const currentLi = document.createElement("li");
      currentLi.className = "text-center w-full cursor-pointer rounded px-1";
      currentLi.dataset.group = "current";
      const currentVal = stat.current != null ? (stat.format ? formatTime(stat.current) : stat.current) : "--";
      currentLi.textContent = currentVal;
      averagesList.appendChild(currentLi);
      if (hasCurrent) {
        currentLi.addEventListener("click", () => copyAverageSolves(stat.min, false));
      }
      const bestLi = document.createElement("li");
      bestLi.className = "text-center w-full cursor-pointer rounded px-1";
      bestLi.dataset.group = "best";
      const bestVal = stat.best != null ? (stat.format ? formatTime(stat.best) : stat.best) : "--";
      bestLi.textContent = bestVal;
      bestLi.title = `Click to copy ${stat.label} times`;
      averagesList.appendChild(bestLi);
      if (hasBest) {
        bestLi.addEventListener("click", () => copyAverageSolves(stat.min, true));
      }
    }
    showStatGroup(showingStatMode);
  } else {
    sessionSec.style.display = "none";
  }
}

function showStatGroup(group) {
  document.querySelectorAll("#averagesList [data-group]").forEach(el => {
    el.style.display = (el.dataset.group === group) ? "block" : "none";
  });
}

function showSolveModal(index) {
  const solve = solveTimes[index];
  const adjusted = solve.ms + (solve.plus2 ? 2000 : 0);
  const formatted = solve.dnf ? "DNF" : formatTime(adjusted);
  const scramble = solve.scramble;
  const copyText = `${formatted}   ${scramble}`;
  Swal.fire({
    title: `#${solveTimes.length - index}: ${formatted}`,
    html: `
      <p id="copySolveText" class="text-lg font-mono cursor-pointer select-text hover:text-blue-500 mb-4">
        ${scramble}
      </p>
      <div class="flex flex-wrap gap-2 justify-center">
        <button id="plus2Button" class="p-2 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 shadow">
          <i class="fa-solid fa-triangle-exclamation"></i>&ensp;${solve.plus2 ? "Remove +2" : "+2"}
        </button>
        <button id="dnfButton" class="p-2 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 shadow">
          <i class="fa-solid fa-circle-xmark"></i>&ensp;${solve.dnf ? "Undo DNF" : "DNF"}
        </button>
        <button id="deleteButton" class="p-2 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 shadow">
          <i class="fa-solid fa-delete-left"></i>&ensp;Delete
        </button>
        <button id="retryButton" class="p-2 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 shadow">
          <i class="fa-solid fa-rotate-right"></i>&ensp;Retry
        </button>
      </div>
    `,
    showConfirmButton: false,
    customClass: {
      popup: 'swal-default-colors'
    }
  });
  setTimeout(() => {
    const textEl = document.getElementById("copySolveText");
    if (textEl) {
      textEl.addEventListener("click", () => {
        navigator.clipboard.writeText(copyText).then(() => {
          showSwal({
            text: "Copied to clipboard.",
            icon: "success"
          });
        });
      });
    }

    const plus2Btn = document.getElementById("plus2Button");
    if (plus2Btn) {
      plus2Btn.addEventListener("click", () => {
        Swal.close();
        solve.plus2 = !solve.plus2;
        localStorage.setItem("solveTimes", JSON.stringify(solveTimes));
        renderTimes();
      });
    }

    const dnfBtn = document.getElementById("dnfButton");
    if (dnfBtn) {
      dnfBtn.addEventListener("click", () => {
        Swal.close();
        solve.dnf = !solve.dnf;
        localStorage.setItem("solveTimes", JSON.stringify(solveTimes));
        renderTimes();
      });
    }

    const deleteBtn = document.getElementById("deleteButton");
    if (deleteBtn) {
      deleteBtn.addEventListener("click", () => {
        Swal.close();
        solveTimes.splice(index, 1);
        localStorage.setItem("solveTimes", JSON.stringify(solveTimes));
        renderTimes();
      });
    }

    const retryBtn = document.getElementById("retryButton");
    if (retryBtn) {
      retryBtn.addEventListener("click", () => {
        Swal.close();

        // Use the unified retry function
        if (index === 0) {
          // For the most recent solve, use the original retrySolve function
          retrySolve();
        } else {
          // For older solves, use the new retrySolveAtIndex function
          retrySolveAtIndex(index);
        }
      });
    }
  }, 50);
}

// Utility functions

// Show notification toast with consistent styling
function showSwal(options) {
  return Swal.fire(Object.assign({
    position: "bottom-end",
    showConfirmButton: false,
    timerProgressBar: true,
    timer: 1500,
    backdrop: false,
    showClass: {
      popup: `animate__animated animate__slideInRight animate__faster`
    },
    hideClass: {
      popup: `animate__animated animate__slideOutRight animate__faster`
    }
  }, options));
}

function toggleLastSolveFlag(flag) {
  const last = solveTimes[0];
  if (last) {
    last[flag] = !last[flag];
    localStorage.setItem("solveTimes", JSON.stringify(solveTimes));
    renderTimes();
  }
}

function copyLastSolveToClipboard() {
  const last = solveTimes[0];
  if (last) {
    const adjusted = last.ms + (last.plus2 ? 2000 : 0);
    const formatted = last.dnf ? "DNF" : formatTime(adjusted);
    const content = `${formatted}   ${last.scramble}`;
    navigator.clipboard.writeText(content).then(() => {
      showSwal({
        text: "Copied to clipboard.",
        icon: "success"
      });
    });
  }
}

function removeLastSolve() {
  if (solveTimes.length > 0) {
    solveTimes.shift();
    localStorage.setItem("solveTimes", JSON.stringify(solveTimes));
    renderTimes();
  }
}

// Load scramble from most recent solve for retry (without deleting the solve)
function retrySolve() {
  if (solveTimes.length > 0) {
    const lastSolve = solveTimes[0];
    const lastScramble = lastSolve.scramble;

    // Set the scramble back to the one from the last solve (without deleting it)
    currentScramble = lastScramble;
    scrambleEl.textContent = currentScramble;

    // Update scramble image
    const img = document.getElementById("scrambleImg");
    const spinner = document.getElementById("scrambleSpinner");
    spinner.classList.remove("hidden");
    img.classList.add("hidden");
    const newSrc = `https://algs.cuber.pro/visualcube/visualcube.php?fmt=png&size=250&bg=t&alg=x2${encodeURIComponent(currentScramble)}`;
    img.onload = () => {
      spinner.classList.add("hidden");
      img.classList.remove("hidden");
    };
    img.src = newSrc;
    updateDiagramVisibility();

    // Add this scramble back to history if it's not already the current one
    if (scrambleHistory[currentScrambleIndex] !== currentScramble) {
      scrambleHistory = scrambleHistory.slice(0, currentScrambleIndex + 1);
      scrambleHistory.push(currentScramble);
      currentScrambleIndex = scrambleHistory.length - 1;
      updateScrambleButtons();
    }

    showSwal({
      text: "Scramble loaded for retry.",
      icon: "info"
    });
  }
}

// Load scramble from a specific solve for retry (without deleting the solve)
function retrySolveAtIndex(index) {
  if (solveTimes.length > index && index >= 0) {
    const solve = solveTimes[index];
    const scramble = solve.scramble;

    // Set the scramble to the selected solve's scramble
    currentScramble = scramble;
    scrambleEl.textContent = currentScramble;

    // Update scramble image
    const img = document.getElementById("scrambleImg");
    const spinner = document.getElementById("scrambleSpinner");
    spinner.classList.remove("hidden");
    img.classList.add("hidden");
    const newSrc = `https://algs.cuber.pro/visualcube/visualcube.php?fmt=png&size=250&bg=t&alg=x2${encodeURIComponent(currentScramble)}`;
    img.onload = () => {
      spinner.classList.add("hidden");
      img.classList.remove("hidden");
    };
    img.src = newSrc;
    updateDiagramVisibility();

    // Add this scramble to history if it's not already there
    if (!scrambleHistory.includes(currentScramble)) {
      scrambleHistory.push(currentScramble);
      currentScrambleIndex = scrambleHistory.length - 1;
      updateScrambleButtons();
    }

    showSwal({
      text: "Scramble loaded for retry.",
      icon: "info"
    });
  }
}

function copyAverageSolves(n, best) {
  const solves = best ? getBestAverageSolves(n) : getAverageSolves(n);
  if (!solves.length) return;
  const content = solves.map(s => {
    const adjusted = s.ms + (s.plus2 ? 2000 : 0);
    const formattedTime = formatTime(adjusted);

    // Add parentheses around excluded times
    const timeDisplay = s.excluded ? `(${formattedTime})` : formattedTime;

    return `${timeDisplay}   ${s.scramble}`;
  }).join("\n");
  navigator.clipboard.writeText(content).then(() => {
    showSwal({
      text: "Copied to clipboard.",
      icon: "success"
    });
  });
}

// Format milliseconds to MM:SS.ss display
function formatTime(ms) {
  ms = Math.round(ms);
  const totalSeconds = ms / 1000;
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = Math.floor(totalSeconds % 60);
  const milliseconds = ms % 1000;
  const dec = milliseconds.toString().padStart(3, "0");
  if (minutes > 0) {
    return `${minutes}:${seconds.toString().padStart(2, "0")}.${dec}`;
  } else {
    return `${seconds}.${dec}`;
  }
}

// Calculate average with trimming (removes fastest/slowest times)
function getAverage(times, n) {
  const recent = times.filter(t => !t.dnf).slice(0, n).map(t => t.ms + (t.plus2 ? 2000 : 0));
  if (recent.length < n) return null;
  const sorted = [...recent].sort((a, b) => a - b);

  // Calculate how many times to trim from each side
  // Default to trimming more rather than less, same amount from each side
  const trimEachSide = Math.ceil(n * 0.05); // Round up to trim more

  const middle = sorted.slice(trimEachSide, n - trimEachSide);
  return middle.reduce((a, b) => a + b, 0) / middle.length;
}

function getAverageSolves(n) {
  const valid = solveTimes.filter(s => !s.dnf).slice(0, n);
  if (valid.length < n) return [];

  // Create array with times and original indices to preserve order
  const withIndices = valid.map((solve, index) => ({
    solve,
    originalIndex: index,
    adjustedTime: solve.ms + (solve.plus2 ? 2000 : 0)
  }));

  // Sort by time to determine which solves to trim
  const sortedByTime = [...withIndices].sort((a, b) => a.adjustedTime - b.adjustedTime);

  // Calculate how many times to trim from each side (same logic as getAverage)
  const trimEachSide = Math.ceil(n * 0.05); // Round up to trim more

  // Mark which solves are excluded
  const excludedIndices = new Set();

  // Add fastest times to excluded set
  for (let i = 0; i < trimEachSide; i++) {
    excludedIndices.add(sortedByTime[i].originalIndex);
  }

  // Add slowest times to excluded set
  for (let i = n - trimEachSide; i < n; i++) {
    excludedIndices.add(sortedByTime[i].originalIndex);
  }

  // Sort back to original chronological order and mark excluded solves
  const result = withIndices
    .sort((a, b) => a.originalIndex - b.originalIndex)
    .map(item => ({
      ...item.solve,
      excluded: excludedIndices.has(item.originalIndex)
    }));

  return result;
}

function getBestAverage(times, n) {
  const sets = [];
  for (let i = 0; i <= times.length - n; i++) {
    const slice = times.slice(i, i + n);
    if (slice.some(t => t.dnf)) continue;
    const vals = slice.map(t => t.ms + (t.plus2 ? 2000 : 0));
    const sorted = [...vals].sort((a, b) => a - b);

    // Use same trimming logic as getAverage
    const trimEachSide = Math.ceil(n * 0.05); // Round up to trim more

    const middle = sorted.slice(trimEachSide, n - trimEachSide);
    sets.push(middle.reduce((a, b) => a + b, 0) / middle.length);
  }
  return sets.length ? Math.min(...sets) : null;
}

function getBestAverageSolves(n) {
  let bestSet = [];
  let bestAvg = Infinity;

  for (let i = 0; i <= solveTimes.length - n; i++) {
    const slice = solveTimes.slice(i, i + n);
    if (slice.some(s => s.dnf)) continue;

    // Create array with times and original indices within this slice
    const withIndices = slice.map((solve, index) => ({
      solve,
      originalIndex: index,
      adjustedTime: solve.ms + (solve.plus2 ? 2000 : 0)
    }));

    // Sort by time to calculate average
    const sortedByTime = [...withIndices].sort((a, b) => a.adjustedTime - b.adjustedTime);

    // Use same trimming logic as other functions
    const trimEachSide = Math.ceil(n * 0.05); // Round up to trim more

    const trimmedForAvg = sortedByTime.slice(trimEachSide, n - trimEachSide);
    const avg = trimmedForAvg.reduce((sum, item) => sum + item.adjustedTime, 0) / trimmedForAvg.length;

    if (avg < bestAvg) {
      bestAvg = avg;

      // Mark which solves are excluded
      const excludedIndices = new Set();

      // Add fastest times to excluded set
      for (let j = 0; j < trimEachSide; j++) {
        excludedIndices.add(sortedByTime[j].originalIndex);
      }

      // Add slowest times to excluded set
      for (let j = n - trimEachSide; j < n; j++) {
        excludedIndices.add(sortedByTime[j].originalIndex);
      }

      // Sort back to original chronological order and mark excluded solves
      bestSet = withIndices
        .sort((a, b) => a.originalIndex - b.originalIndex)
        .map(item => ({
          ...item.solve,
          excluded: excludedIndices.has(item.originalIndex)
        }));
    }
  }

  return bestSet;
}

function getBestSingle(times) {
  const best = times
    .filter(s => !s.dnf)
    .reduce((best, curr) => {
      const currMs = curr.ms + (curr.plus2 ? 2000 : 0);
      return !best || currMs < best.ms ? { ...curr, ms: currMs } : best;
    }, null);
  return best ? formatTime(best.ms) : "--";
}

// Start 15-second inspection period with time announcements
function startInspection() {
  inspecting = true;
  inspectionStartTime = Date.now();
  inspectionAnnounced8 = false;
  inspectionAnnounced12 = false;
  latestTime.textContent = "Inspection: 0.000";
  latestTime.className = "text-yellow-500";
  inspectionInterval = setInterval(() => {
    const elapsed = (Date.now() - inspectionStartTime) / 1000;
    latestTime.textContent = `Inspection: ${elapsed.toFixed(3)}`;
    if (!inspectionAnnounced8 && elapsed >= 7.75) {
      inspectionAnnounced8 = true;
      speakInspection("8 seconds");
    }
    if (!inspectionAnnounced12 && elapsed >= 11.75) {
      inspectionAnnounced12 = true;
      speakInspection("12 seconds");
    }
  }, 31);
}

function endInspectionAndStartSolve() {
  // Stop inspection timer
  clearInterval(inspectionInterval);
  inspecting = false;
  const inspectionTime = (Date.now() - inspectionStartTime) / 1000;
  // Penalty logic
  let penalty = null;
  if (inspectionTime > 17) {
    penalty = "dnf";
  } else if (inspectionTime > 15) {
    penalty = "plus2";
  }
  // Start solve as normal
  readyToStart = false;
  manualRunning = true;
  latestTime.className = "text-black dark:text-white";
  manualStartTime = Date.now();
  runningInterval = setInterval(() => {
    const elapsed = Date.now() - manualStartTime;
    latestTime.textContent = formatTime(elapsed);
  }, 31);
  // Store penalty for use when solve ends
  window._pendingInspectionPenalty = penalty;
  window._pendingInspectionTime = inspectionTime;
}

function cancelInspection() {
  clearInterval(inspectionInterval);
  inspectionStartup = false;
  clearTimeout(inspectionStartupTimer);
  inspecting = false;
  latestTime.textContent = "0.000";
  latestTime.className = "text-black dark:text-white";
}

function speakInspection(text) {
  if ("speechSynthesis" in window) {
    if (window.speechSynthesis.getVoices().length === 0) {
      window.speechSynthesis.getVoices();
    }
    const utter = new SpeechSynthesisUtterance(text);
    utter.rate = 1.5;
    window.speechSynthesis.speak(utter);
  }
}